"""
简单的API测试脚本
运行前请确保后端服务器已启动 (python run.py)
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """测试健康检查"""
    response = requests.get("http://localhost:8000/health")
    print("Health Check:", response.json())

def test_register_user():
    """测试用户注册"""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "testpass123",
        "full_name": "Test User"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=user_data)
    print("Register User:", response.status_code, response.json())
    return response.status_code == 200

def test_login_user():
    """测试用户登录"""
    login_data = {
        "username": "<EMAIL>",  # FastAPI OAuth2PasswordRequestForm uses 'username' field for email
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    print("Login User:", response.status_code)
    
    if response.status_code == 200:
        token_data = response.json()
        print("Token:", token_data)
        return token_data["access_token"]
    return None

def test_get_products():
    """测试获取商品列表"""
    response = requests.get(f"{BASE_URL}/products/")
    print("Get Products:", response.status_code, len(response.json()) if response.status_code == 200 else response.json())

def test_get_categories():
    """测试获取分类列表"""
    response = requests.get(f"{BASE_URL}/products/categories/")
    print("Get Categories:", response.status_code, len(response.json()) if response.status_code == 200 else response.json())

def test_protected_endpoint(token):
    """测试需要认证的端点"""
    if not token:
        print("No token available for protected endpoint test")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/users/me", headers=headers)
    print("Get Current User:", response.status_code, response.json())

def main():
    print("=== Nike Trade Platform API 测试 ===\n")
    
    # 测试健康检查
    test_health_check()
    print()
    
    # 测试用户注册
    test_register_user()
    print()
    
    # 测试用户登录
    token = test_login_user()
    print()
    
    # 测试商品相关接口
    test_get_products()
    print()
    
    test_get_categories()
    print()
    
    # 测试需要认证的接口
    test_protected_endpoint(token)
    print()
    
    print("=== 测试完成 ===")
    print("请访问 http://localhost:8000/docs 查看完整的API文档")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到后端服务器")
        print("请确保后端服务器已启动: python run.py")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
