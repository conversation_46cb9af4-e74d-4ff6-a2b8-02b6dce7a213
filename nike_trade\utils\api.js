// API配置和请求工具
const BASE_URL = 'http://localhost:8000/api/v1'

// 请求拦截器
const request = (url, options = {}) => {
  const token = uni.getStorageSync('access_token')
  
  const defaultOptions = {
    method: 'GET',
    header: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    },
    ...options
  }

  return new Promise((resolve, reject) => {
    uni.request({
      url: `${BASE_URL}${url}`,
      ...defaultOptions,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// API接口
export const api = {
  // 用户认证
  auth: {
    // 用户注册
    register: (userData) => request('/auth/register', {
      method: 'POST',
      data: userData
    }),
    
    // 用户登录
    login: (email, password) => request('/auth/login', {
      method: 'POST',
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: `username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
    })
  },

  // 用户管理
  users: {
    // 获取当前用户信息
    getCurrentUser: () => request('/users/me'),
    
    // 更新用户信息
    updateProfile: (userData) => request('/users/me', {
      method: 'PUT',
      data: userData
    })
  },

  // 商品管理
  products: {
    // 获取商品列表
    getProducts: (params = {}) => {
      const query = new URLSearchParams(params).toString()
      return request(`/products/${query ? '?' + query : ''}`)
    },
    
    // 获取商品详情
    getProduct: (productId) => request(`/products/${productId}`),
    
    // 搜索商品
    searchProducts: (keyword, page = 0, limit = 20) => 
      request(`/products/?search=${encodeURIComponent(keyword)}&skip=${page * limit}&limit=${limit}`),
    
    // 获取分类商品
    getProductsByCategory: (categoryId, page = 0, limit = 20) =>
      request(`/products/?category_id=${categoryId}&skip=${page * limit}&limit=${limit}`),
    
    // 获取特色商品
    getFeaturedProducts: (page = 0, limit = 20) =>
      request(`/products/?featured=true&skip=${page * limit}&limit=${limit}`)
  },

  // 分类管理
  categories: {
    // 获取所有分类
    getCategories: () => request('/products/categories/')
  }
}

// 工具函数
export const utils = {
  // 保存登录token
  saveToken: (token) => {
    uni.setStorageSync('access_token', token)
  },
  
  // 清除登录token
  clearToken: () => {
    uni.removeStorageSync('access_token')
  },
  
  // 检查是否已登录
  isLoggedIn: () => {
    return !!uni.getStorageSync('access_token')
  },
  
  // 处理API错误
  handleError: (error) => {
    console.error('API Error:', error)
    
    if (error.statusCode === 401) {
      // 未授权，清除token并跳转到登录页
      utils.clearToken()
      uni.navigateTo({
        url: '/pages/login/login'
      })
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      })
    } else if (error.statusCode >= 500) {
      uni.showToast({
        title: '服务器错误，请稍后重试',
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: error.data?.detail || '请求失败',
        icon: 'none'
      })
    }
  }
}

export default api
