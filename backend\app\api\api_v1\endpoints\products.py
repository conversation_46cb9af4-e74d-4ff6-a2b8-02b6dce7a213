from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app import models, schemas
from app.api import deps
from app.crud import crud_product, crud_category

router = APIRouter()


@router.get("/", response_model=List[schemas.Product])
def read_products(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = Query(default=100, lte=100),
    category_id: Optional[int] = None,
    featured: Optional[bool] = None,
    search: Optional[str] = None,
) -> Any:
    """
    Retrieve products.
    """
    if search:
        products = crud_product.search(db, query=search, skip=skip, limit=limit)
    elif category_id:
        products = crud_product.get_by_category(
            db, category_id=category_id, skip=skip, limit=limit
        )
    elif featured:
        products = crud_product.get_featured(db, skip=skip, limit=limit)
    else:
        products = crud_product.get_multi(db, skip=skip, limit=limit)
    return products


@router.get("/{product_id}", response_model=schemas.Product)
def read_product(
    *,
    db: Session = Depends(deps.get_db),
    product_id: int,
) -> Any:
    """
    Get product by ID.
    """
    product = crud_product.get(db, id=product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    return product


@router.post("/", response_model=schemas.Product)
def create_product(
    *,
    db: Session = Depends(deps.get_db),
    product_in: schemas.ProductCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new product.
    """
    product = crud_product.create(db, obj_in=product_in)
    return product


@router.put("/{product_id}", response_model=schemas.Product)
def update_product(
    *,
    db: Session = Depends(deps.get_db),
    product_id: int,
    product_in: schemas.ProductUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a product.
    """
    product = crud_product.get(db, id=product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    product = crud_product.update(db, db_obj=product, obj_in=product_in)
    return product


@router.get("/categories/", response_model=List[schemas.Category])
def read_categories(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve categories.
    """
    categories = crud_category.get_active(db)
    return categories
