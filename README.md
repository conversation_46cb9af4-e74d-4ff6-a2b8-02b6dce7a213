# Nike Trade Platform

一个基于uni-app前端和FastAPI后端的Nike交易平台项目。

## 项目架构

### 前端 (nike_trade/)
- **框架**: uni-app (Vue 3 + TypeScript)
- **平台支持**: 小程序、H5、App
- **主要功能**: 商品展示、用户认证、购物车、订单管理

### 后端 (backend/)
- **框架**: FastAPI (Python 3.8+)
- **数据库**: SQLite (开发环境) / PostgreSQL (生产环境)
- **认证**: JWT Token
- **API文档**: 自动生成的OpenAPI/Swagger文档

## 项目结构

```
nike_platform/
├── nike_trade/                 # uni-app前端项目
│   ├── App.uvue               # 应用入口文件
│   ├── pages/                 # 页面文件
│   ├── static/                # 静态资源
│   ├── manifest.json          # 应用配置
│   └── pages.json             # 页面路由配置
├── backend/                   # FastAPI后端项目
│   ├── app/                   # 应用核心代码
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── crud/              # 数据库操作
│   │   ├── db/                # 数据库配置
│   │   ├── models/            # 数据模型
│   │   ├── schemas/           # Pydantic模型
│   │   └── main.py            # FastAPI应用入口
│   ├── requirements.txt       # Python依赖
│   ├── .env.example          # 环境变量模板
│   ├── run.py                # 启动脚本
│   ├── start.bat             # Windows启动脚本
│   └── start.sh              # Linux/Mac启动脚本
└── README.md                 # 项目文档
```

## 快速开始

### 后端启动

#### Windows
```bash
cd backend
start.bat
```

#### Linux/Mac
```bash
cd backend
chmod +x start.sh
./start.sh
```

#### 手动启动
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 复制环境配置文件
cp .env.example .env

# 启动服务器
python run.py
```

### 前端启动

使用HBuilderX或其他uni-app开发工具打开`nike_trade`目录，然后运行到相应平台。

## API文档

后端启动后，可以通过以下地址访问API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要功能

### 用户管理
- 用户注册/登录
- JWT认证
- 用户信息管理

### 商品管理
- 商品列表/详情
- 分类管理
- 商品搜索
- 特色商品

### 订单系统
- 购物车管理
- 订单创建/查询
- 订单状态管理
- 支付集成

## 数据库模型

### 用户表 (users)
- 基本信息：邮箱、用户名、密码
- 个人资料：姓名、电话、头像
- 状态管理：激活状态、验证状态

### 商品表 (products)
- 商品信息：名称、描述、价格
- 商品属性：品牌、型号、颜色、尺码
- 库存管理：库存数量、状态
- 分类关联：商品分类

### 订单表 (orders)
- 订单信息：订单号、总金额
- 状态管理：订单状态、支付状态
- 地址信息：收货地址、账单地址
- 用户关联：订单用户

### 订单项表 (order_items)
- 商品详情：商品、数量、单价
- 订单关联：所属订单

### 收藏表 (favorites)
- 用户收藏的商品列表

## 环境配置

### 后端环境变量 (.env)
```env
# 数据库配置
DATABASE_URL=sqlite:///./nike_trade.db

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API配置
API_V1_STR=/api/v1
PROJECT_NAME=Nike Trade Platform API
DEBUG=True

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
```

## 开发指南

### 添加新的API端点
1. 在`app/schemas/`中定义Pydantic模型
2. 在`app/models/`中定义SQLAlchemy模型
3. 在`app/crud/`中实现CRUD操作
4. 在`app/api/api_v1/endpoints/`中创建路由
5. 在`app/api/api_v1/api.py`中注册路由

### 数据库迁移
项目使用SQLAlchemy自动创建表结构。如需更复杂的迁移，可以集成Alembic。

## 部署

### 生产环境配置
1. 使用PostgreSQL替代SQLite
2. 设置强密码和安全的SECRET_KEY
3. 配置HTTPS
4. 使用Gunicorn或uWSGI作为WSGI服务器
5. 配置Nginx作为反向代理

### Docker部署
```dockerfile
# 可以创建Dockerfile进行容器化部署
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建Issue或联系开发团队。
