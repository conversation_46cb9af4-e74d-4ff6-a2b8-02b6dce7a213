import uvicorn
from app.main import app
from app.db.database import SessionLocal
from app.db.init_db import init_db, create_first_superuser

if __name__ == "__main__":
    # Initialize database
    db = SessionLocal()
    try:
        init_db(db)
        create_first_superuser(db)
        print("Database initialized successfully!")
    except Exception as e:
        print(f"Error initializing database: {e}")
    finally:
        db.close()
    
    # Start the server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
