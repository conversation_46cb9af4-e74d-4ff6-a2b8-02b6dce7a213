from sqlalchemy.orm import Session
from app import models
from app.core.config import settings
from app.db.database import engine
from app.crud import crud_user, crud_category, crud_product
from app.schemas import UserCreate, CategoryCreate, ProductCreate


def init_db(db: Session) -> None:
    # Create tables
    models.Base.metadata.create_all(bind=engine)
    
    # Create default categories
    categories_data = [
        {"name": "运动鞋", "description": "各种运动鞋款", "image_url": "/static/categories/shoes.jpg"},
        {"name": "服装", "description": "运动服装系列", "image_url": "/static/categories/clothing.jpg"},
        {"name": "配件", "description": "运动配件", "image_url": "/static/categories/accessories.jpg"},
        {"name": "限量版", "description": "限量发售商品", "image_url": "/static/categories/limited.jpg"},
    ]
    
    for category_data in categories_data:
        category = crud_category.get_by_name(db, name=category_data["name"])
        if not category:
            category_in = CategoryCreate(**category_data)
            crud_category.create(db, obj_in=category_in)
    
    # Create sample products
    shoes_category = crud_category.get_by_name(db, name="运动鞋")
    if shoes_category:
        sample_products = [
            {
                "name": "Nike Air Max 270",
                "description": "经典气垫跑鞋，舒适透气",
                "price": 899.0,
                "original_price": 1099.0,
                "brand": "Nike",
                "model": "Air Max 270",
                "color": "黑白",
                "size": "42",
                "stock_quantity": 50,
                "image_urls": '["https://example.com/airmax270_1.jpg", "https://example.com/airmax270_2.jpg"]',
                "is_featured": True,
                "category_id": shoes_category.id
            },
            {
                "name": "Nike React Infinity Run",
                "description": "专业跑步鞋，提供卓越缓震",
                "price": 1299.0,
                "brand": "Nike",
                "model": "React Infinity Run",
                "color": "蓝色",
                "size": "41",
                "stock_quantity": 30,
                "image_urls": '["https://example.com/react_1.jpg", "https://example.com/react_2.jpg"]',
                "category_id": shoes_category.id
            }
        ]
        
        for product_data in sample_products:
            existing_product = db.query(models.Product).filter(
                models.Product.name == product_data["name"]
            ).first()
            if not existing_product:
                product_in = ProductCreate(**product_data)
                crud_product.create(db, obj_in=product_in)


def create_first_superuser(db: Session) -> None:
    user = crud_user.get_by_email(db, email="<EMAIL>")
    if not user:
        user_in = UserCreate(
            email="<EMAIL>",
            username="admin",
            password="admin123",
            full_name="Nike Admin",
        )
        user = crud_user.create(db, obj_in=user_in)
