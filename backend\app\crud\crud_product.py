from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.product import Product, Category
from app.schemas.product import ProductCreate, ProductUpdate, CategoryCreate, CategoryUpdate


class CRUDProduct(CRUDBase[Product, ProductCreate, ProductUpdate]):
    def get_by_category(
        self, db: Session, *, category_id: int, skip: int = 0, limit: int = 100
    ) -> List[Product]:
        return (
            db.query(self.model)
            .filter(Product.category_id == category_id)
            .filter(Product.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_featured(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[Product]:
        return (
            db.query(self.model)
            .filter(Product.is_featured == True)
            .filter(Product.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def search(
        self, db: Session, *, query: str, skip: int = 0, limit: int = 100
    ) -> List[Product]:
        return (
            db.query(self.model)
            .filter(Product.name.contains(query))
            .filter(Product.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDCategory(CRUDBase[Category, CategoryCreate, CategoryUpdate]):
    def get_active(self, db: Session) -> List[Category]:
        return db.query(self.model).filter(Category.is_active == True).all()

    def get_by_name(self, db: Session, *, name: str) -> Optional[Category]:
        return db.query(self.model).filter(Category.name == name).first()


crud_product = CRUDProduct(Product)
crud_category = CRUDCategory(Category)
